using System;
using System.Linq;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Threading;

namespace Imip.HotelFrontOffice.Reservations.BackgroundWorkers;

/// <summary>
/// Background worker for checking and closing reservations that should be closed
/// based on payment status and checkout status
/// </summary>
public class ReservationClosureWorker : AsyncPeriodicBackgroundWorkerBase
{
    private readonly ILogger<ReservationClosureWorker> _logger;

    public ReservationClosureWorker(
        AbpAsyncTimer timer,
        IServiceScopeFactory serviceScopeFactory,
        ILogger<ReservationClosureWorker> logger)
        : base(timer, serviceScopeFactory)
    {
        _logger = logger;
        // Run every 5 minutes
        timer.Period = 5 * 60 * 1000;
    }

    protected override async Task DoWorkAsync(PeriodicBackgroundWorkerContext workerContext)
    {
        try
        {
            _logger.LogInformation("Starting reservation closure check...");

            var masterStatusRepository = workerContext.ServiceProvider.GetRequiredService<IRepository<MasterStatus, Guid>>();
            var reservationRepository = workerContext.ServiceProvider.GetRequiredService<IReservationRepository>();
            var reservationDetailsRepository = workerContext.ServiceProvider.GetRequiredService<IReservationDetailsRepository>();
            var reservationFoodAndBeverageRepository = workerContext.ServiceProvider.GetRequiredService<IReservationFoodAndBeveragesRepository>();
            var reservationRoomRepository = workerContext.ServiceProvider.GetRequiredService<IReservationRoomsRepository>();

            // Get required status records
            var checkOutStatus = await GetMasterStatusByDocTypeAndCodeAsync(masterStatusRepository, "ReservationDetails", "CheckOut");
            var paidStatus = await GetMasterStatusByDocTypeAndCodeAsync(masterStatusRepository, "PaymentStatus", "Paid");
            var reservationClosedStatus = await GetMasterStatusByDocTypeAndCodeAsync(masterStatusRepository, "Reservations", "Close");

            if (checkOutStatus == null || paidStatus == null || reservationClosedStatus == null)
            {
                _logger.LogWarning("Could not find required status records. CheckOut: {CheckOut}, Paid: {Paid}, Close: {Close}",
                    checkOutStatus?.Id, paidStatus?.Id, reservationClosedStatus?.Id);
                return;
            }

            // Find reservation details with 'Check OUT' status
            var checkedOutReservationDetails = await reservationDetailsRepository.GetListAsync(rd =>
                rd.StatusId == checkOutStatus.Id && !rd.IsDeleted);

            _logger.LogInformation("Found {Count} reservation details with CheckOut status", checkedOutReservationDetails.Count);

            var processedReservations = 0;
            var closedReservations = 0;

            // Group by reservation to avoid processing the same reservation multiple times
            var reservationGroups = checkedOutReservationDetails.GroupBy(rd => rd.ReservationId);

            foreach (var reservationGroup in reservationGroups)
            {
                var reservationId = reservationGroup.Key;
                processedReservations++;

                try
                {
                    // Check if this reservation should be closed
                    var shouldClose = await ShouldCloseReservationAsync(
                        reservationId,
                        paidStatus.Id,
                        reservationDetailsRepository,
                        reservationFoodAndBeverageRepository,
                        reservationRoomRepository);

                    if (shouldClose)
                    {
                        // Check if reservation is already closed
                        var reservation = await reservationRepository.GetAsync(reservationId);
                        if (reservation.StatusId != reservationClosedStatus.Id)
                        {
                            // Close the reservation
                            reservation.StatusId = reservationClosedStatus.Id;
                            await reservationRepository.UpdateAsync(reservation);
                            closedReservations++;

                            _logger.LogInformation("Closed reservation {ReservationId} as all related entities are paid and checked out", reservationId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing reservation {ReservationId}: {Message}", reservationId, ex.Message);
                }
            }

            _logger.LogInformation("Reservation closure check completed. Processed: {Processed}, Closed: {Closed}", 
                processedReservations, closedReservations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in reservation closure worker: {Message}", ex.Message);
        }
    }

    /// <summary>
    /// Gets a MasterStatus by DocType and Code
    /// </summary>
    private async Task<MasterStatus?> GetMasterStatusByDocTypeAndCodeAsync(
        IRepository<MasterStatus, Guid> repository, 
        string docType, 
        string code)
    {
        var statuses = await repository.GetListAsync(s => 
            s.DocType == docType && s.Code == code);
        return statuses.FirstOrDefault();
    }

    /// <summary>
    /// Checks if a reservation should be closed based on payment status
    /// </summary>
    private async Task<bool> ShouldCloseReservationAsync(
        Guid reservationId,
        Guid paidStatusId,
        IReservationDetailsRepository reservationDetailsRepository,
        IReservationFoodAndBeveragesRepository reservationFoodAndBeverageRepository,
        IReservationRoomsRepository reservationRoomRepository)
    {
        // Get all reservation details for this reservation
        var reservationDetails = await reservationDetailsRepository.GetListAsync(rd =>
            rd.ReservationId == reservationId && !rd.IsDeleted);

        if (reservationDetails.Count == 0)
        {
            return false; // No reservation details found
        }

        // Check if all reservation details have paid status
        foreach (var reservationDetail in reservationDetails)
        {
            if (reservationDetail.PaymentStatusId != paidStatusId)
            {
                return false;
            }

            // Check all ReservationFoodAndBeverage records for this reservation detail
            var foodAndBeverages = await reservationFoodAndBeverageRepository.GetListAsync(fb =>
                fb.ReservationDetailsId == reservationDetail.Id && !fb.IsDeleted);

            foreach (var fb in foodAndBeverages)
            {
                if (fb.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }

            // Check all ReservationRoom records for this reservation detail
            var reservationRooms = await reservationRoomRepository.GetListAsync(rr =>
                rr.ReservationDetailsId == reservationDetail.Id && !rr.IsDeleted);

            foreach (var room in reservationRooms)
            {
                if (room.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }
        }

        return true; // All entities have paid status
    }
}

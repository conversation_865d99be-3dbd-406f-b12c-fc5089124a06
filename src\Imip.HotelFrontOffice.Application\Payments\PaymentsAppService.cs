﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Attachments;
using Imip.HotelFrontOffice.Common;
using Imip.HotelFrontOffice.MasterStatuses;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.PaymentDetails;
using Imip.HotelFrontOffice.PaymentGuests;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.ReservationDetails;
using Imip.HotelFrontOffice.ReservationFoodAndBeverages;
using Imip.HotelFrontOffice.ReservationRooms;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Uow;
using Volo.Abp.Validation;

namespace Imip.HotelFrontOffice.Payments;

[Route("api/app/payments")]
[Authorize(WismaAppPermissions.PolicyPayment.Default)]
public class PaymentsAppService : CrudAppService<
    Payment,
    PaymentsDto,
    Guid,
    PagedAndSortedResultRequestDto,
    CreateUpdatePaymentsDto,
    CreateUpdatePaymentsDto
>, IPaymentsAppService
{
    private readonly IRepository<Payment, Guid> _repository;
    private readonly IRepository<Reservations.Reservation, Guid> _reservationRepository;
    private readonly IRepository<ReservationDetail, Guid> _reservationDetailsRepository;
    private readonly IRepository<PaymentDetail, Guid> _paymentDetailRepository;
    private readonly IRepository<PaymentGuest, Guid> _paymentGuestRepository;
    private readonly IRepository<ReservationFoodAndBeverage, Guid> _reservationFoodAndBeverageRepository;
    private readonly IRepository<ReservationRoom, Guid> _reservationRoomRepository;
    private readonly IRepository<MasterStatus, Guid> _masterStatusRepository;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly ILogger<PaymentsAppService> _logger;
    private readonly IPaymentCodeGeneratorService _codeGeneratorService;
    private readonly IAttachmentAppService _attachmentAppService;
    private readonly IGuidGenerator _guidGenerator;

    public PaymentsAppService(
        IRepository<Payment, Guid> repository,
        IRepository<Reservations.Reservation, Guid> reservationRepository,
        IRepository<ReservationDetail, Guid> reservationDetailsRepository,
        IRepository<PaymentDetail, Guid> paymentDetailRepository,
        IRepository<PaymentGuest, Guid> paymentGuestRepository,
        IRepository<ReservationFoodAndBeverage, Guid> reservationFoodAndBeverageRepository,
        IRepository<ReservationRoom, Guid> reservationRoomRepository,
        IRepository<MasterStatus, Guid> masterStatusRepository,
        IUnitOfWorkManager unitOfWorkManager,
        ILogger<PaymentsAppService> logger,
        IPaymentCodeGeneratorService codeGeneratorService,
        IAttachmentAppService attachmentAppService,
        IGuidGenerator guidGenerator) : base(repository)
    {
        _repository = repository;
        _reservationRepository = reservationRepository;
        _reservationDetailsRepository = reservationDetailsRepository;
        _paymentDetailRepository = paymentDetailRepository;
        _paymentGuestRepository = paymentGuestRepository;
        _reservationFoodAndBeverageRepository = reservationFoodAndBeverageRepository;
        _reservationRoomRepository = reservationRoomRepository;
        _masterStatusRepository = masterStatusRepository;
        _unitOfWorkManager = unitOfWorkManager;
        _logger = logger;
        _codeGeneratorService = codeGeneratorService;
        _guidGenerator = guidGenerator;
        _attachmentAppService = attachmentAppService;
    }

    [HttpGet]
    [Authorize(WismaAppPermissions.PolicyPayment.View)]
    public override Task<PagedResultDto<PaymentsDto>> GetListAsync(PagedAndSortedResultRequestDto input)
    {
        return base.GetListAsync(input);
    }

    [HttpPost]
    [Authorize(WismaAppPermissions.PolicyPayment.Create)]
    public override async Task<PaymentsDto> CreateAsync(CreateUpdatePaymentsDto input)
    {
        try
        {
            // Verify reservation and reservation details exist
            var reservation = await _reservationRepository.GetAsync(input.ReservationsId);

            if (reservation == null)
            {
                throw new UserFriendlyException("Reservation not found");
            }

            // Validate payment details if provided
            if (input.PaymentDetails != null && input.PaymentDetails.Count > 0)
            {
                await ValidatePaymentDetailsAsync(input.PaymentDetails);
            }

            // Validate payment guests if provided
            // if (input.PaymentGuests != null && input.PaymentGuests.Count > 0)
            // {
            //     ValidatePaymentGuests(input.PaymentGuests);
            // }

            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            try
            {
                // Create the payment entity without payment details
                var entity = ObjectMapper.Map<CreateUpdatePaymentsDto, Payment>(input);
                entity.PaymentCode = await _codeGeneratorService.GeneratePaymentCode();

                // We don't need to set PaymentDetails to null since we've configured AutoMapper to ignore it

                await _repository.InsertAsync(entity);

                // Create payment details separately
                if (input.PaymentDetails != null && input.PaymentDetails.Count > 0)
                {
                    // Pass the payment status ID to handle cancellations
                    await CreatePaymentDetailsAsync(entity.Id, input.PaymentDetails, entity.StatusId);
                }

                // Check if reservation should be closed after payment details creation
                await CheckAndCloseReservationIfFullyPaidAsync(input.ReservationsId);

                // Process general payment attachments if provided
                if (input.PaymentAttachments != null && input.PaymentAttachments.Count > 0)
                {
                    await ProcessPaymentAttachmentsAsync(input.PaymentAttachments, entity.Id, "Payment");
                }

                // Process-reservation-related payment attachments if provided
                if (input.ReservationAttachments != null && input.ReservationAttachments.Count > 0)
                {
                    await ProcessPaymentAttachmentsAsync(input.ReservationAttachments, entity.Id, "PaymentReservation");
                }

                // Complete the transaction
                await uow.CompleteAsync();

                // Get the complete payment with details and guests
                var result = await GetPaymentWithDetailsAndGuestsAsync(entity.Id);
                return result;
            }
            catch (UserFriendlyException)
            {
                throw;
            }
            catch (Exception ex)
            {
                // Roll back the transaction on error
                await uow.RollbackAsync();
                _logger.LogError(ex, "Error creating payment with details and guests: {Message}", ex.Message);
                throw new UserFriendlyException(
                    "Failed to create payment with details and guests",
                    "Error.Payment.CreateFailed",
                    ex.Message);
            }
        }
        catch (UserFriendlyException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating payment: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Failed to create payment",
                "Error.Payment.CreateFailed",
                ex.Message);
        }
    }

    [HttpPut("{id}")]
    [Authorize(WismaAppPermissions.PolicyPayment.Edit)]
    public override async Task<PaymentsDto> UpdateAsync(Guid id, CreateUpdatePaymentsDto input)
    {
        try
        {
            // Verify reservation and reservation details exist
            var reservation = await _reservationRepository.GetAsync(input.ReservationsId);

            if (reservation == null)
            {
                throw new UserFriendlyException("Reservation or Reservation Details not found");
            }

            // Validate payment details if provided
            if (input.PaymentDetails != null && input.PaymentDetails.Count > 0)
            {
                await ValidatePaymentDetailsAsync(input.PaymentDetails);
            }

            // Validate payment guests if provided
            // if (input.PaymentGuests != null && input.PaymentGuests.Count > 0)
            // {
            //     ValidatePaymentGuests(input.PaymentGuests);
            // }

            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);
            try
            {
                // Get the existing payment entity
                var entity = await _repository.GetAsync(id);

                // Update the payment entity, but don't update PaymentDetails through mapping
                // We'll handle that separately
                await MapToEntityAsync(input, entity);
                await _repository.UpdateAsync(entity);

                // Update payment details if provided
                if (input.PaymentDetails != null && input.PaymentDetails.Count > 0)
                {
                    await UpdatePaymentDetailsAsync(entity.Id, input.PaymentDetails, entity.StatusId);
                }

                // Update payment guests if provided
                // if (input.PaymentGuests != null && input.PaymentGuests.Count > 0)
                // {
                //     await UpdatePaymentGuestsAsync(entity.Id, input.PaymentGuests);
                // }

                // Process general payment attachments if provided
                if (input.PaymentAttachments != null && input.PaymentAttachments.Count > 0)
                {
                    await ProcessPaymentAttachmentsAsync(input.PaymentAttachments, entity.Id, "Payment");
                }

                // Process-reservation-related payment attachments if provided
                if (input.ReservationAttachments != null && input.ReservationAttachments.Count > 0)
                {
                    await ProcessPaymentAttachmentsAsync(input.ReservationAttachments, entity.Id, "PaymentReservation");
                }

                // Complete the transaction
                await uow.CompleteAsync();

                // Get the complete payment with details and guests
                var result = await GetPaymentWithDetailsAndGuestsAsync(entity.Id);
                return result;
            }
            catch (Exception ex)
            {
                // Roll back the transaction on error
                await uow.RollbackAsync();
                _logger.LogError(ex, "Error updating payment with details and guests: {Message}", ex.Message);
                throw new UserFriendlyException(
                    "Failed to update payment with details and guests",
                    "Error.Payment.UpdateFailed",
                    ex.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating payment: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Failed to update payment",
                "Error.Payment.UpdateFailed",
                ex.Message);
        }
    }

    /// <summary>
    /// Validates payment details before creating or updating
    /// </summary>
    private async Task ValidatePaymentDetailsAsync(List<CreateUpdatePaymentDetailsDto> paymentDetails)
    {
        var validationErrors = new List<ValidationResult>();

        foreach (var detail in paymentDetails)
        {
            var reservationDetails = await _reservationDetailsRepository.GetAsync(detail.ReservationDetailsId);
            if (reservationDetails == null)
            {
                validationErrors.Add(new ValidationResult(
                    "Reservation Detail not found.",
                    new[] { nameof(detail.ReservationDetailsId) }
                ));
            }

            // Validate that the SourceType is a valid enum value
            if (!Enum.IsDefined(typeof(PaymentSourceType), detail.SourceType))
            {
                validationErrors.Add(new ValidationResult(
                    "Invalid source type. Must be one of the valid payment source types.",
                    new[] { nameof(detail.SourceType) }
                ));
            }

            // Validate that the SourceId is not empty
            if (detail.SourceId == Guid.Empty)
            {
                validationErrors.Add(new ValidationResult(
                    "Source ID is required.",
                    new[] { nameof(detail.SourceId) }
                ));
            }

            // Validate that the Amount is positive
            if (detail.Amount <= 0)
            {
                validationErrors.Add(new ValidationResult(
                    "Amount must be greater than zero.",
                    new[] { nameof(detail.Amount) }
                ));
            }

            // Validate that the Qty is positive
            if (detail.Qty <= 0)
            {
                validationErrors.Add(new ValidationResult(
                    "Quantity must be greater than zero.",
                    new[] { nameof(detail.Qty) }
                ));
            }
        }

        if (validationErrors.Count > 0)
        {
            throw new AbpValidationException(
                "Invalid payment details",
                validationErrors);
        }
    }

    /// <summary>
    /// Validates payment guests before creating or updating
    /// </summary>
    private void ValidatePaymentGuests(List<CreateUpdatePaymentGuestsDto> paymentGuests)
    {
        var validationErrors = new List<ValidationResult>();

        foreach (var guest in paymentGuests)
        {
            // Validate that the GuestId is not empty
            if (guest.GuestId == Guid.Empty)
            {
                validationErrors.Add(new ValidationResult(
                    "Guest ID is required.",
                    new[] { nameof(guest.GuestId) }
                ));
            }

            // Validate that the AmountPaid is positive
            if (guest.AmountPaid <= 0)
            {
                validationErrors.Add(new ValidationResult(
                    "Amount paid must be greater than zero.",
                    new[] { nameof(guest.AmountPaid) }
                ));
            }
        }

        if (validationErrors.Count > 0)
        {
            throw new AbpValidationException(
                "Invalid payment guests",
                validationErrors);
        }
    }

    /// <summary>
    /// Checks if all related entities for a reservation have paid status and closes the reservation if needed
    /// </summary>
    /// <param name="reservationId">The ID of the reservation to check</param>
    private async Task CheckAndCloseReservationIfFullyPaidAsync(Guid reservationId)
    {
        try
        {
            // Get the paid status for payment status
            var paidStatus = await GetMasterStatusByDocTypeAndCodeAsync("paymentStatus", "paid");
            if (paidStatus == null)
            {
                _logger.LogWarning("Could not find MasterStatus with DocType='PaymentStatus' and Code='Paid'. Skipping reservation closure check.");
                return;
            }

            // Check if all entities for this reservation have paid status
            var allEntitiesPaid = await CheckAllEntitiesPaidStatusAsync(reservationId, paidStatus.Id);

            if (allEntitiesPaid)
            {
                // Close the reservation
                await CloseReservationAsync(reservationId);
                _logger.LogInformation("Reservation {ReservationId} has been closed as all related entities are paid.", reservationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking and closing reservation {ReservationId}: {Message}", reservationId, ex.Message);
            // Don't throw - this is a secondary operation that shouldn't fail the main payment creation
        }
    }

    /// <summary>
    /// Gets a MasterStatus by DocType and Code
    /// </summary>
    /// <param name="docType">The document type</param>
    /// <param name="code">The status code</param>
    /// <returns>The MasterStatus if found, null otherwise</returns>
    private async Task<MasterStatus?> GetMasterStatusByDocTypeAndCodeAsync(string docType, string code)
    {
        var statuses = await _masterStatusRepository.GetListAsync(s =>
            s.DocType == docType && s.Code == code);
        return statuses.FirstOrDefault();
    }

    /// <summary>
    /// Checks if all related entities for a reservation have paid status
    /// </summary>
    /// <param name="reservationId">The reservation ID</param>
    /// <param name="paidStatusId">The paid status ID</param>
    /// <returns>True if all entities are paid, false otherwise</returns>
    private async Task<bool> CheckAllEntitiesPaidStatusAsync(Guid reservationId, Guid paidStatusId)
    {
        // Get all reservation details for this reservation
        var reservationDetails = await _reservationDetailsRepository.GetListAsync(rd =>
            rd.ReservationId == reservationId && !rd.IsDeleted);

        if (!reservationDetails.Any())
        {
            return false; // No reservation details found
        }

        // Check if all reservation details have paid status
        foreach (var reservationDetail in reservationDetails)
        {
            if (reservationDetail.PaymentStatusId != paidStatusId)
            {
                return false;
            }

            // Check all ReservationFoodAndBeverage records for this reservation detail
            var foodAndBeverages = await _reservationFoodAndBeverageRepository.GetListAsync(fb =>
                fb.ReservationDetailsId == reservationDetail.Id && !fb.IsDeleted);

            foreach (var fb in foodAndBeverages)
            {
                if (fb.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }

            // Check all ReservationRoom records for this reservation detail
            var reservationRooms = await _reservationRoomRepository.GetListAsync(rr =>
                rr.ReservationDetailsId == reservationDetail.Id && !rr.IsDeleted);

            foreach (var room in reservationRooms)
            {
                if (room.PaymentStatusId != paidStatusId)
                {
                    return false;
                }
            }
        }

        return true; // All entities have paid status
    }

    /// <summary>
    /// Closes a reservation by updating its status to closed
    /// </summary>
    /// <param name="reservationId">The reservation ID to close</param>
    private async Task CloseReservationAsync(Guid reservationId)
    {
        // Get the closed status for reservations
        var closedStatus = await GetMasterStatusByDocTypeAndCodeAsync("reservations", "close");
        if (closedStatus == null)
        {
            _logger.LogWarning("Could not find MasterStatus with DocType='Reservations' and Code='Close'. Cannot close reservation {ReservationId}.", reservationId);
            return;
        }

        // Update the reservation status
        var reservation = await _reservationRepository.GetAsync(reservationId);
        reservation.StatusId = closedStatus.Id;
        await _reservationRepository.UpdateAsync(reservation);
    }

    /// <summary>
    /// Creates payment details for a payment
    /// </summary>
    /// <param name="paymentId">The ID of the payment</param>
    /// <param name="paymentDetails">The payment details to create</param>
    /// <param name="paymentStatusId">Optional payment status ID for cancellation handling</param>
    private async Task CreatePaymentDetailsAsync(Guid paymentId, List<CreateUpdatePaymentDetailsDto> paymentDetails, Guid? paymentStatusId = null)
    {
        var detailEntities = new List<PaymentDetail>();
        var processedSourceIds = new HashSet<string>(); // Track processed source IDs to avoid duplicates

        // Find the "Paid" payment status (case-insensitive, using EF Core compatible approach)
        // First get all payment statuses and then filter in memory
        var paymentStatuses = await _masterStatusRepository
            .GetListAsync(s => s.DocType != null && s.DocType.ToLower().Contains("paymentstatus"));

        var paidStatus = paymentStatuses
            .FirstOrDefault(s => s.Code != null && s.Code.ToLower().Contains("paid"));

        if (paidStatus == null)
        {
            _logger.LogWarning("Could not find MasterStatus with DocType='PaymentStatus' and Code='Paid'. Source tables will not be updated.");
        }

        // Check if the provided paymentStatusId is for a "Canceled" status
        MasterStatus? canceledStatus = null;
        if (paymentStatusId.HasValue)
        {
            // First, get the status by ID
            var status = await _masterStatusRepository.FindAsync(paymentStatusId.Value);

            // Then check if it's a canceled status
            if (status != null && status.Code != null && status.Code.ToLower().Contains("canceled"))
            {
                canceledStatus = status;
            }
        }

        foreach (var detailDto in paymentDetails)
        {
            // Always generate a new GUID for each payment detail to avoid conflicts
            var detailId = _guidGenerator.Create();

            // Set the payment ID on the DTO for reference
            detailDto.PaymentId = paymentId;

            // Create a unique tracking key for this detail
            var trackingKey = $"{detailDto.SourceType}_{detailDto.SourceId}_{detailDto.ReservationDetailsId}";

            // Skip if we've already processed this combination to avoid tracking conflicts
            if (processedSourceIds.Contains(trackingKey))
            {
                _logger.LogWarning("Skipping duplicate payment detail with source type {SourceType}, source ID {SourceId}, and reservation details ID {ReservationDetailsId}",
                    detailDto.SourceType, detailDto.SourceId, detailDto.ReservationDetailsId);
                continue;
            }

            // Create the payment detail entity using the constructor with a new GUID
            var detailEntity = new PaymentDetail(
                detailId,  // Use a new GUID for each entity
                paymentId,
                detailDto.SourceType,
                detailDto.SourceId,
                detailDto.ReservationDetailsId,
                detailDto.Amount,
                detailDto.Qty,
      detailDto.UnitPrice
            );

            // Add to our list and mark as processed
            detailEntities.Add(detailEntity);
            processedSourceIds.Add(trackingKey);

            // Update the source table based on the SourceType and payment status
            if (canceledStatus != null)
            {
                // If we have a canceled status, use that
                await UpdateSourceTablePaymentStatusAsync(detailDto.SourceType, detailDto.SourceId, canceledStatus.Id, true);
            }
            else if (paidStatus != null)
            {
                // Otherwise use the paid status if available
                await UpdateSourceTablePaymentStatusAsync(detailDto.SourceType, detailDto.SourceId, paidStatus.Id);
            }
        }

        // If we have any entities to insert, do it in a single batch
        if (detailEntities.Count > 0)
        {
            // Insert directly without a nested unit of work
            await _paymentDetailRepository.InsertManyAsync(detailEntities);
        }
    }

    /// <summary>
    /// Checks if all child entities of a reservation detail are paid and updates the reservation detail's payment status accordingly
    /// </summary>
    /// <param name="reservationDetail">The reservation detail to check and update</param>
    /// <param name="paidStatusId">The ID of the "Paid" status</param>
    private async Task CheckAndUpdateReservationDetailPaymentStatusAsync(ReservationDetail reservationDetail, Guid paidStatusId)
    {
        try
        {
            // Find the "Pending" payment status (case-insensitive, using EF Core compatible approach)
            // First get all payment statuses and then filter in memory
            var paymentStatuses = await _masterStatusRepository
                .GetListAsync(s => s.DocType != null && s.DocType.ToLower().Contains("paymentstatus"));

            var pendingStatus = paymentStatuses
                .FirstOrDefault(s => s.Code != null && s.Code.ToLower().Contains("pending"));

            if (pendingStatus == null)
            {
                _logger.LogWarning("Could not find MasterStatus with DocType='PaymentStatus' and Code='Pending'. Using default behavior.");
                // If we can't find a pending status, just update with the paid status
                reservationDetail.PaymentStatusId = paidStatusId;
                await _reservationDetailsRepository.UpdateAsync(reservationDetail);
                return;
            }

            // Get all related food and beverage items for this reservation detail
            var foodAndBeverages = await _reservationFoodAndBeverageRepository.GetListAsync(
                fb => fb.ReservationDetailsId == reservationDetail.Id && !fb.IsDeleted);

            // Get all related room services for this reservation detail
            var roomServices = await _reservationRoomRepository.GetListAsync(
                rs => rs.ReservationDetailsId == reservationDetail.Id && !rs.IsDeleted);

            // Check if all child entities have the "Paid" status
            bool allChildrenPaid = true;

            // Check food and beverage items
            foreach (var fb in foodAndBeverages)
            {
                if (fb.PaymentStatusId != paidStatusId)
                {
                    allChildrenPaid = false;
                    break;
                }
            }

            // If all food and beverage items are paid, check room services
            if (allChildrenPaid)
            {
                foreach (var rs in roomServices)
                {
                    if (rs.PaymentStatusId != paidStatusId)
                    {
                        allChildrenPaid = false;
                        break;
                    }
                }
            }

            // Update the reservation detail's payment status based on the check
            if (allChildrenPaid)
            {
                // All children are paid, so mark the reservation detail as paid
                reservationDetail.PaymentStatusId = paidStatusId;
                await _reservationDetailsRepository.UpdateAsync(reservationDetail);
            }
            else
            {
                // Some children are not paid, so mark the reservation detail as pending
                reservationDetail.PaymentStatusId = pendingStatus.Id;
                await _reservationDetailsRepository.UpdateAsync(reservationDetail);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking and updating reservation detail payment status for ID {Id}: {Message}",
                reservationDetail.Id, ex.Message);

            // In case of error, update with the paid status to avoid blocking the payment process
            reservationDetail.PaymentStatusId = paidStatusId;
            await _reservationDetailsRepository.UpdateAsync(reservationDetail);
        }
    }

    /// <summary>
    /// Updates the payment status in the source table based on the payment source type
    /// </summary>
    private async Task UpdateSourceTablePaymentStatusAsync(PaymentSourceType sourceType, Guid sourceId, Guid paidStatusId, bool isCancellation = false)
    {
        try
        {
            switch (sourceType)
            {
                case PaymentSourceType.ReservationRoom:
                    // Update AppReservationDetails table
                    var reservationDetail = await _reservationDetailsRepository.FindAsync(sourceId);
                    if (reservationDetail != null)
                    {
                        if (!isCancellation)
                        {
                            // Check if all child entities are paid before marking the reservation detail as paid
                            await CheckAndUpdateReservationDetailPaymentStatusAsync(reservationDetail, paidStatusId);
                        }
                        else
                        {
                            // For cancellations, directly update the status
                            reservationDetail.PaymentStatusId = paidStatusId;
                            await _reservationDetailsRepository.UpdateAsync(reservationDetail);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("ReservationDetail with ID {Id} not found", sourceId);
                    }
                    break;

                case PaymentSourceType.ReservationRoomFoodAndBeverage:
                    // Update AppReservationFoodAndBeverages table
                    var foodAndBeverage = await _reservationFoodAndBeverageRepository.FindAsync(sourceId);
                    if (foodAndBeverage != null)
                    {
                        foodAndBeverage.PaymentStatusId = paidStatusId;
                        await _reservationFoodAndBeverageRepository.UpdateAsync(foodAndBeverage);

                        // After updating the food and beverage, check and update the parent reservation detail
                        if (!isCancellation)
                        {
                            var reservationDetailFnb = await _reservationDetailsRepository.FindAsync(foodAndBeverage.ReservationDetailsId);
                            if (reservationDetailFnb != null)
                            {
                                await CheckAndUpdateReservationDetailPaymentStatusAsync(reservationDetailFnb, paidStatusId);
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning("ReservationFoodAndBeverage with ID {Id} not found", sourceId);
                    }
                    break;

                case PaymentSourceType.ReservationRoomService:
                    // Update AppReservationRoom table
                    var roomService = await _reservationRoomRepository.FindAsync(sourceId);
                    if (roomService != null)
                    {
                        roomService.PaymentStatusId = paidStatusId;
                        await _reservationRoomRepository.UpdateAsync(roomService);

                        // After updating the room service, check and update the parent reservation detail
                        if (!isCancellation)
                        {
                            var reservationDetailRoom = await _reservationDetailsRepository.FindAsync(roomService.ReservationDetailsId);
                            if (reservationDetailRoom != null)
                            {
                                await CheckAndUpdateReservationDetailPaymentStatusAsync(reservationDetailRoom, paidStatusId);
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning("ReservationRoom with ID {Id} not found", sourceId);
                    }
                    break;

                default:
                    _logger.LogWarning("Unknown source type: {SourceType}", sourceType);
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating source table for source type {SourceType} and source ID {SourceId}: {Message}",
                sourceType, sourceId, ex.Message);
            // Don't rethrow the exception to avoid failing the payment creation
        }
    }

    /// <summary>
    /// Override MapToEntityAsync to ensure we don't map PaymentDetails
    /// </summary>
    protected override Task MapToEntityAsync(CreateUpdatePaymentsDto updateInput, Payment entity)
    {
        // Save the existing payment details to restore after mapping
        var existingPaymentDetails = entity.PaymentDetails;

        // Map all properties except PaymentDetails
        ObjectMapper.Map(updateInput, entity);

        // Restore the original payment details
        entity.PaymentDetails = existingPaymentDetails;

        return Task.CompletedTask;
    }

    /// <summary>
    /// Updates payment details for a payment
    /// </summary>
    /// <param name="paymentId">The ID of the payment</param>
    /// <param name="paymentDetails">The payment details to update</param>
    /// <param name="paymentStatusId">Optional payment status ID for cancellation handling</param>
    private async Task UpdatePaymentDetailsAsync(Guid paymentId, List<CreateUpdatePaymentDetailsDto> paymentDetails, Guid? paymentStatusId = null)
    {
        // Get existing payment details for this payment
        var existingDetails = await _paymentDetailRepository.GetListAsync(d => d.PaymentId == paymentId);

        // Create a dictionary of existing details by ID for quick lookup
        var existingDetailsDict = existingDetails.ToDictionary(d => d.Id, d => d);
        // Check if the provided paymentStatusId is for a "Canceled" status
        MasterStatus? canceledStatus = null;
        if (paymentStatusId.HasValue)
        {
            // First get the status by ID
            var status = await _masterStatusRepository.FindAsync(paymentStatusId.Value);

            // Then check if it's a canceled status
            if (status != null && status.Code != null && status.Code.ToLower().Contains("canceled"))
            {
                canceledStatus = status;
            }

            if (canceledStatus != null)
            {
                // Update all source tables for existing payment details with the canceled status
                foreach (var detail in existingDetails)
                {
                    if (detail.SourceType.HasValue && detail.SourceId.HasValue)
                    {
                        await UpdateSourceTablePaymentStatusAsync(detail.SourceType.Value, detail.SourceId.Value, canceledStatus.Id, true);
                    }
                }
            }
        }

        // Track processed combinations to avoid duplicates
        var processedCombinations = new HashSet<string>();
        var detailsToUpdate = new List<PaymentDetail>();
        var detailsToInsert = new List<PaymentDetail>();

        foreach (var detailDto in paymentDetails)
        {
            // Set the payment ID
            detailDto.PaymentId = paymentId;

            // Create a unique tracking key for this detail
            var trackingKey = $"{detailDto.SourceType}_{detailDto.SourceId}_{detailDto.ReservationDetailsId}";

            // Skip if we've already processed this combination to avoid tracking conflicts
            if (processedCombinations.Contains(trackingKey))
            {
                _logger.LogWarning("Skipping duplicate payment detail with source type {SourceType}, source ID {SourceId}, and reservation details ID {ReservationDetailsId}",
                    detailDto.SourceType, detailDto.SourceId, detailDto.ReservationDetailsId);
                continue;
            }

            // Check if this is an existing detail or a new one
            if (detailDto.Id != Guid.Empty && existingDetailsDict.TryGetValue(detailDto.Id, out var existingDetail))
            {
                // Update existing detail
                ObjectMapper.Map(detailDto, existingDetail);
                detailsToUpdate.Add(existingDetail);

                // Remove from the dictionary to track which ones were processed
                existingDetailsDict.Remove(existingDetail.Id);
            }
            else
            {
                // Create new detail using the constructor
                var detailEntity = new PaymentDetail(
                   _guidGenerator.Create(),
                   detailDto.PaymentId,
                   detailDto.SourceType,
                   detailDto.SourceId,
                   detailDto.ReservationDetailsId,
                   detailDto.Amount,
                   detailDto.Qty,
                   detailDto.UnitPrice
               );

                detailsToInsert.Add(detailEntity);
            }

            // Mark this combination as processed
            processedCombinations.Add(trackingKey);
        }

        // Batch update existing details
        if (detailsToUpdate.Count > 0)
        {
            await _paymentDetailRepository.UpdateManyAsync(detailsToUpdate);
        }

        // Batch inserts new details
        if (detailsToInsert.Count > 0)
        {
            await _paymentDetailRepository.InsertManyAsync(detailsToInsert);
        }

        // Delete any details that weren't in the update list
        if (existingDetailsDict.Count > 0)
        {
            await _paymentDetailRepository.DeleteManyAsync(existingDetailsDict.Values);
        }
    }

    /// <summary>
    /// Creates payment guests for a payment
    /// </summary>
    private async Task CreatePaymentGuestsAsync(Guid paymentId, List<CreateUpdatePaymentGuestsDto> paymentGuests)
    {
        var guestEntities = new List<PaymentGuest>();
        var processedGuestIds = new HashSet<Guid>(); // Track processed guest IDs to avoid duplicates

        foreach (var guestDto in paymentGuests)
        {
            // Skip if we've already processed this guest to avoid tracking conflicts
            if (processedGuestIds.Contains(guestDto.GuestId))
            {
                _logger.LogWarning("Skipping duplicate payment guest with guest ID {GuestId}", guestDto.GuestId);
                continue;
            }

            // Create the payment guest entity using the constructor with a new GUID
            var guestEntity = new PaymentGuest(
                _guidGenerator.Create(),
                paymentId,
                guestDto.GuestId,
                guestDto.AmountPaid
            );

            guestEntities.Add(guestEntity);
            processedGuestIds.Add(guestDto.GuestId);
        }

        // Insert all entities in a single batch if we have any
        if (guestEntities.Count > 0)
        {
            await _paymentGuestRepository.InsertManyAsync(guestEntities);
        }
    }

    /// <summary>
    /// Updates payment guests for a payment
    /// </summary>
    private async Task UpdatePaymentGuestsAsync(Guid paymentId, List<CreateUpdatePaymentGuestsDto> paymentGuests)
    {
        // Get existing payment guests for this payment
        var existingGuests = await _paymentGuestRepository.GetListAsync(g => g.PaymentId == paymentId);

        // Create a dictionary of existing guests by ID for a quick lookup
        var existingGuestsDict = existingGuests.ToDictionary(g => g.Id, g => g);

        // Track processed guest IDs to avoid duplicates
        var processedGuestIds = new HashSet<Guid>();
        var guestsToUpdate = new List<PaymentGuest>();
        var guestsToInsert = new List<PaymentGuest>();

        foreach (var guestDto in paymentGuests)
        {
            // Skip if we've already processed this guest to avoid tracking conflicts
            if (processedGuestIds.Contains(guestDto.GuestId))
            {
                _logger.LogWarning("Skipping duplicate payment guest with guest ID {GuestId}", guestDto.GuestId);
                continue;
            }

            // Check if this is an existing guest or a new one
            if (guestDto.Id != Guid.Empty && existingGuestsDict.TryGetValue(guestDto.Id, out var existingGuest))
            {
                // Update existing guest
                existingGuest.GuestId = guestDto.GuestId;
                existingGuest.AmountPaid = guestDto.AmountPaid;

                guestsToUpdate.Add(existingGuest);

                // Remove from the dictionary to track which ones were processed
                existingGuestsDict.Remove(existingGuest.Id);
            }
            else
            {
                // Create a new guest using the constructor
                var guestEntity = new PaymentGuest(
                    _guidGenerator.Create(),
                    paymentId,
                    guestDto.GuestId,
                    guestDto.AmountPaid
                );

                guestsToInsert.Add(guestEntity);
            }

            // Mark this guest ID as processed
            processedGuestIds.Add(guestDto.GuestId);
        }

        // Update and insert in batches
        if (guestsToUpdate.Count > 0)
        {
            await _paymentGuestRepository.UpdateManyAsync(guestsToUpdate);
        }

        if (guestsToInsert.Count > 0)
        {
            await _paymentGuestRepository.InsertManyAsync(guestsToInsert);
        }

        // Delete any guests that weren't in the update list
        if (existingGuestsDict.Count > 0)
        {
            await _paymentGuestRepository.DeleteManyAsync(existingGuestsDict.Values);
        }
    }

    /// <summary>
    /// Process payment attachments and upload them to the SFTP server
    /// </summary>
    private async Task ProcessPaymentAttachmentsAsync<T>(List<T> attachments, Guid paymentId, string referenceType)
        where T : PaymentAttachmentDto
    {
        foreach (var attachment in attachments)
        {
            try
            {
                // Validate the base64 string
                if (string.IsNullOrEmpty(attachment.Base64Content))
                {
                    Logger.LogWarning("Empty base64 content for file {FileName}", attachment.FileName);
                    continue;
                }

                // Validate file type
                if (!IsAllowedFileType(attachment.ContentType))
                {
                    Logger.LogWarning("Invalid file type {ContentType} for file {FileName}",
                        attachment.ContentType, attachment.FileName);
                    continue;
                }

                // Decode the base64 string to a byte array
                byte[] fileBytes;
                try
                {
                    fileBytes = Convert.FromBase64String(attachment.Base64Content);
                }
                catch (FormatException ex)
                {
                    Logger.LogWarning(ex, "Invalid base64 string for file {FileName}", attachment.FileName);
                    continue;
                }

                // Create file upload DTO
                var fileUploadDto = new FileUploadDto
                {
                    Description = attachment.Description ?? $"Payment attachment for payment ID {paymentId}",
                    ReferenceId = paymentId,
                    ReferenceType = referenceType
                };

                // Upload a file
                await _attachmentAppService.UploadFileAsync(
                    fileUploadDto,
                    attachment.FileName,
                    attachment.ContentType,
                    fileBytes);

                // Clean up old attachments, keeping only the latest one
                await CleanupOldAttachmentsAsync(paymentId, referenceType);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error processing payment attachment {FileName}: {Message}",
                    attachment.FileName, ex.Message);
                // Continue with other attachments even if one fails
            }
        }
    }

    /// <summary>
    /// Cleans up old attachments for a payment, keeping only the latest one
    /// </summary>
    private async Task CleanupOldAttachmentsAsync(Guid paymentId, string referenceType)
    {
        try
        {
            // Get all attachments for this payment and reference type
            var attachments = await _attachmentAppService.GetByReferenceAsync(paymentId, referenceType);

            // If there are 2 or more attachments, delete all but the latest one
            if (attachments.Count > 1)
            {
                // Sort by creation time (newest first)
                var sortedAttachments = attachments.OrderByDescending(a => a.UploadTime).ToList();

                // Keep the first one (latest), delete the rest
                for (int i = 1; i < sortedAttachments.Count; i++)
                {
                    await _attachmentAppService.DeleteAsync(sortedAttachments[i].Id);
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't throw, as this is a cleanup operation
            Logger.LogError(ex, "Error cleaning up old payment attachments for payment {PaymentId} and reference type {ReferenceType}",
                paymentId, referenceType);
        }
    }

    /// <summary>
    /// Checks if the file type is allowed (PDF or image)
    /// </summary>
    private static bool IsAllowedFileType(string contentType)
    {
        if (string.IsNullOrEmpty(contentType))
            return false;

        contentType = contentType.ToLower();

        // Allow PDF files
        if (contentType == "application/pdf")
            return true;

        // Allow image files
        if (contentType.StartsWith("image/"))
            return true;

        return false;
    }

    [HttpGet("{id}")]
    [Authorize(WismaAppPermissions.PolicyPayment.View)]
    public override async Task<PaymentsDto> GetAsync(Guid id)
    {
        return await GetPaymentWithDetailsAndGuestsAsync(id);
    }

    /// <summary>
    /// Cleans up all attachments for a payment, keeping only the latest one for each reference type
    /// </summary>
    [HttpPost]
    [Route("/api/app/payments/{paymentId}/cleanup-attachments")]
    [Authorize(WismaAppPermissions.PolicyPayment.Edit)]
    [ProducesResponseType(typeof(bool), StatusCodes.Status200OK)]
    public async Task<bool> CleanupPaymentAttachmentsAsync(Guid paymentId)
    {
        try
        {
            // Use a unit of work to ensure transaction consistency
            using var uow = _unitOfWorkManager.Begin(requiresNew: true, isTransactional: true);

            try
            {
                // Clean up payment attachments
                await CleanupOldAttachmentsAsync(paymentId, "Payment");

                // Clean up payment-reservation attachments
                await CleanupOldAttachmentsAsync(paymentId, "PaymentReservation");

                // Complete the transaction
                await uow.CompleteAsync();

                return true;
            }
            catch (Exception ex)
            {
                await uow.RollbackAsync();
                Logger.LogError(ex, "Error cleaning up payment attachments for payment {PaymentId}", paymentId);
                throw new UserFriendlyException(L["FailedToCleanupAttachments"], ex.Message);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error cleaning up payment attachments for payment {PaymentId}", paymentId);
            throw new UserFriendlyException(L["FailedToCleanupAttachments"], ex.Message);
        }
    }

    /// <summary>
    /// Gets a payment with its details and guests
    /// </summary>
    private async Task<PaymentsDto> GetPaymentWithDetailsAndGuestsAsync(Guid paymentId)
    {
        var query = await _repository.GetQueryableAsync();

        var payment = await query
            .AsNoTracking()
            .Include(p => p.PaymentDetails)
            .Include(p => p.PaymentGuests)
            .Include(p => p.PaymentMethod)
            .Include(p => p.Status)
            .Include(p => p.Reservations)
            .FirstOrDefaultAsync(p => p.Id == paymentId);

        if (payment == null)
        {
            throw new EntityNotFoundException(typeof(Payment), paymentId);
        }

        var paymentDto = ObjectMapper.Map<Payment, PaymentsDto>(payment);

        // Get general payment attachments
        try
        {
            var paymentAttachments = await _attachmentAppService.GetByReferenceAsync(paymentId, "Payment");
            if (paymentAttachments != null && paymentAttachments.Count > 0)
            {
                paymentDto.PaymentAttachments = paymentAttachments.Select(a => new PaymentAttachmentInfoDto
                {
                    Id = a.Id,
                    FileName = a.FileName,
                    ContentType = a.ContentType,
                    Size = a.Size,
                    Url = a.Url,
                    StreamUrl = a.StreamUrl,
                    Description = null, // Description is not available in FileUploadResultDto
                    CreationTime = a.UploadTime
                }).ToList();
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the entire request
            Logger.LogError(ex, "Error retrieving payment attachments for payment {PaymentId}: {Message}",
                paymentId, ex.Message);
        }

        // Get reservation-related payment attachments
        try
        {
            var reservationAttachments = await _attachmentAppService.GetByReferenceAsync(paymentId, "PaymentReservation");
            if (reservationAttachments != null && reservationAttachments.Count > 0)
            {
                paymentDto.ReservationAttachments = reservationAttachments.Select(a => new PaymentReservationAttachmentInfoDto
                {
                    Id = a.Id,
                    FileName = a.FileName,
                    ContentType = a.ContentType,
                    Size = a.Size,
                    Url = a.Url,
                    StreamUrl = a.StreamUrl,
                    Description = null, // Description is not available in FileUploadResultDto
                    CreationTime = a.UploadTime
                }).ToList();
            }
        }
        catch (Exception ex)
        {
            // Log the error but don't fail the entire request
            Logger.LogError(ex, "Error retrieving reservation-related payment attachments for payment {PaymentId}: {Message}",
                paymentId, ex.Message);
        }

        return paymentDto;
    }

    [HttpDelete("{id}")]
    [Authorize(WismaAppPermissions.PolicyPayment.Delete)]
    public override Task DeleteAsync(Guid id)
    {
        return base.DeleteAsync(id);
    }
}

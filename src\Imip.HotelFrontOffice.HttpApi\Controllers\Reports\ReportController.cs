using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Imip.HotelFrontOffice.Models;
using Imip.HotelFrontOffice.Permissions;
using Imip.HotelFrontOffice.Reports;
using Imip.HotelFrontOffice.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Imip.HotelFrontOffice.Controllers.Reports;

[Route("api/report")]
[RemoteService]
public class ReportController : HotelFrontOfficeController
{
    private readonly IReportAppService _reportAppService;
    private readonly IRepository<Report> _repository;
    private readonly ILogger<ReportController> _logger;

    public ReportController(
        IReportAppService reportAppService,
        IRepository<Report> repository,
        ILogger<ReportController> logger)
    {
        _reportAppService = reportAppService;
        _repository = repository;
        _logger = logger;
    }

    [HttpGet]
    public async Task<PagedResultDto<ReportDto>> GetListAsync([FromQuery] PagedAndSortedResultRequestDto input)
    {
        return await _reportAppService.GetListAsync(input);
    }

    [HttpGet("active")]
    public async Task<List<ReportDto>> GetActiveReportsAsync()
    {
        return await _reportAppService.GetActiveReportsAsync();
    }

    [HttpGet("{id}")]
    public async Task<ReportDto> GetAsync(Guid id)
    {
        return await _reportAppService.GetAsync(id);
    }

    [HttpPost]
    public async Task<ReportDto> CreateAsync(CreateUpdateReportDto input)
    {
        return await _reportAppService.CreateAsync(input);
    }

    [HttpPut("{id}")]
    public async Task<ReportDto> UpdateAsync(Guid id, CreateUpdateReportDto input)
    {
        return await _reportAppService.UpdateAsync(id, input);
    }

    [HttpDelete("{id}")]
    public async Task DeleteAsync(Guid id)
    {
        await _reportAppService.DeleteAsync(id);
    }

    [HttpPost("preview")]
    public async Task<ReportPreviewDto> PreviewReportAsync(ReportExecutionDto input)
    {
        return await _reportAppService.PreviewReportAsync(input);
    }

    [HttpPost("export/csv")]
    public async Task<IActionResult> ExportToCsvAsync(ReportExecutionDto input)
    {
        var data = await _reportAppService.ExportReportToCsvAsync(input);
        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "text/csv", $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.csv");
    }

    [HttpPost("export/excel")]
    public async Task<IActionResult> ExportToExcelAsync(ReportExecutionDto input)
    {
        var data = await _reportAppService.ExportReportToExcelAsync(input);
        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
    }

    [HttpPost("export/excel/custom")]
    public async Task<IActionResult> ExportToExcelWithCustomHeaderAsync(ReportExecutionWithCustomHeaderDto input)
    {
        var executionDto = new ReportExecutionDto
        {
            ReportId = input.ReportId,
            Parameters = input.Parameters
        };

        byte[] data;
        if (input.CustomHeader != null)
        {
            data = await _reportAppService.ExportReportToExcelWithCustomHeaderAsync(executionDto, input.CustomHeader);
        }
        else
        {
            data = await _reportAppService.ExportReportToExcelAsync(executionDto);
        }

        var report = await _reportAppService.GetAsync(input.ReportId);

        return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            $"{report.Name}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
    }

    [HttpGet("{reportId}/parameters")]
    public async Task<List<ReportParameterDto>> GetReportParametersAsync(Guid reportId)
    {
        return await _reportAppService.GetReportParametersAsync(reportId);
    }

    [HttpGet("{reportId}/excel-header")]
    public async Task<ReportExcelHeaderDto> GetReportExcelHeaderConfigAsync(Guid reportId)
    {
        return await _reportAppService.GetReportExcelHeaderConfigAsync(reportId);
    }

    [HttpPut("{reportId}/excel-header")]
    public async Task UpdateReportExcelHeaderConfigAsync(Guid reportId, ReportExcelHeaderDto headerConfig)
    {
        await _reportAppService.UpdateReportExcelHeaderConfigAsync(reportId, headerConfig);
    }

    [HttpGet("{reportId}/debug-header")]
    public async Task<IActionResult> DebugReportHeaderAsync(Guid reportId)
    {
        try
        {
            var report = await _reportAppService.GetAsync(reportId);
            var headerConfig = await _reportAppService.GetReportExcelHeaderConfigAsync(reportId);
            var parameters = await _reportAppService.GetReportParametersAsync(reportId);

            // Try to manually parse the JSON to get more detailed error information
            string? headerParseError = null;
            string? parameterParseError = null;

            if (!string.IsNullOrEmpty(report.ExcelHeaderConfig) && headerConfig == null)
            {
                try
                {
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    };
                    System.Text.Json.JsonSerializer.Deserialize<object>(report.ExcelHeaderConfig, options);
                }
                catch (Exception ex)
                {
                    headerParseError = ex.Message;
                }
            }

            if (!string.IsNullOrEmpty(report.Parameters) && (parameters == null || parameters.Count == 0))
            {
                try
                {
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true
                    };
                    System.Text.Json.JsonSerializer.Deserialize<object>(report.Parameters, options);
                }
                catch (Exception ex)
                {
                    parameterParseError = ex.Message;
                }
            }

            return Ok(new
            {
                ReportName = report.Name,
                HasExcelHeaderConfig = !string.IsNullOrEmpty(report.ExcelHeaderConfig),
                ExcelHeaderConfigRaw = report.ExcelHeaderConfig,
                ParsedHeaderConfig = headerConfig,
                HeaderRowsCount = headerConfig?.HeaderRows?.Count ?? 0,
                FirstHeaderRowCellsCount = headerConfig?.HeaderRows?.FirstOrDefault()?.Cells?.Count ?? 0,
                HeaderParseError = headerParseError,
                HasParameters = !string.IsNullOrEmpty(report.Parameters),
                ParametersRaw = report.Parameters,
                ParsedParameters = parameters,
                ParametersCount = parameters?.Count ?? 0,
                ParameterParseError = parameterParseError
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    [HttpPost("{reportId}/test-header")]
    public async Task<IActionResult> TestReportWithProgrammaticHeaderAsync(Guid reportId, ReportExecutionDto input)
    {
        try
        {
            // Create header configuration programmatically
            var customHeader = new ReportExcelHeaderDto
            {
                Title = "Laporan Bulanan Kamar",
                SubTitle = "Statistik Penggunaan Kamar",
                ShowGeneratedDate = true,
                DateFormat = "dd MMMM yyyy HH:mm:ss",
                HeaderRows = new List<ReportExcelHeaderRowDto>
                {
                    new ReportExcelHeaderRowDto
                    {
                        Cells = new List<ReportExcelHeaderCellDto>
                        {
                            new ReportExcelHeaderCellDto
                            {
                                Text = "Informasi Kamar",
                                ColSpan = 7,
                                RowSpan = 1,
                                Style = new ReportExcelStyleDto
                                {
                                    Bold = true,
                                    BackgroundColor = "#4472C4",
                                    FontColor = "#FFFFFF",
                                    HorizontalAlignment = "Center",
                                    Border = true
                                }
                            }
                        }
                    },
                    new ReportExcelHeaderRowDto
                    {
                        Cells = new List<ReportExcelHeaderCellDto>
                        {
                            new ReportExcelHeaderCellDto { Text = "Tanggal Laporan" },
                            new ReportExcelHeaderCellDto { Text = "Total Kamar" },
                            new ReportExcelHeaderCellDto { Text = "MTC" },
                            new ReportExcelHeaderCellDto { Text = "LS" },
                            new ReportExcelHeaderCellDto { Text = "Semua Tamu" },
                            new ReportExcelHeaderCellDto { Text = "Sisa Kamar" },
                            new ReportExcelHeaderCellDto { Text = "Kosong (%)" }
                        }
                    }
                }
            };

            var data = await _reportAppService.ExportReportToExcelWithCustomHeaderAsync(input, customHeader);
            var report = await _reportAppService.GetAsync(input.ReportId);

            return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                $"{report.Name}_Test_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    [HttpPost("export/excel/conditional-formatting-test")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    public async Task<IActionResult> ExportReportWithConditionalFormattingTestAsync(ReportExecutionDto input)
    {
        try
        {
            var customHeader = new ReportExcelHeaderDto
            {
                Title = "Report with Conditional Formatting",
                SubTitle = "Demonstrating cell background colors based on values",
                ShowGeneratedDate = true,
                TitleStyle = new ReportExcelStyleDto
                {
                    FontSize = 18,
                    Bold = true,
                    HorizontalAlignment = "Center",
                    BackgroundColor = "#E6F3FF"
                },
                HeaderStyle = new ReportExcelStyleDto
                {
                    Bold = true,
                    BackgroundColor = "#D4EDDA",
                    HorizontalAlignment = "Center",
                    Border = true
                },
                DataStyle = new ReportExcelStyleDto
                {
                    Border = true
                },
                ColumnConfigs = new List<ReportExcelColumnConfigDto>
                {
                    // Example: Status column with conditional formatting
                    new ReportExcelColumnConfigDto
                    {
                        ColumnName = "Status", // Adjust column name based on your report
                        ConditionalRules = new List<ConditionalFormattingRuleDto>
                        {
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.SpecificValue,
                                Operator = ConditionalFormattingOperator.Equals,
                                ComparisonValue = "Paid",
                                BackgroundColor = "#D4EDDA", // Light green
                                Priority = 1
                            },
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.SpecificValue,
                                Operator = ConditionalFormattingOperator.Equals,
                                ComparisonValue = "Pending",
                                BackgroundColor = "#FFF3CD", // Light yellow
                                Priority = 2
                            },
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.SpecificValue,
                                Operator = ConditionalFormattingOperator.Equals,
                                ComparisonValue = "Overdue",
                                BackgroundColor = "#F8D7DA", // Light red
                                Priority = 3
                            }
                        }
                    },
                    // Example: Amount column with numeric conditional formatting
                    new ReportExcelColumnConfigDto
                    {
                        ColumnName = "Amount", // Adjust column name based on your report
                        CellType = ExcelCellType.Currency,
                        ConditionalRules = new List<ConditionalFormattingRuleDto>
                        {
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.NumericComparison,
                                Operator = ConditionalFormattingOperator.GreaterThan,
                                ComparisonValue = "1000",
                                BackgroundColor = "#CCE5FF", // Light blue for high amounts
                                Priority = 1
                            },
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.NumericComparison,
                                Operator = ConditionalFormattingOperator.LessThan,
                                ComparisonValue = "0",
                                BackgroundColor = "#FFCCCC", // Light red for negative amounts
                                Priority = 2
                            }
                        }
                    },
                    // Example: Description column with text pattern matching
                    new ReportExcelColumnConfigDto
                    {
                        ColumnName = "Description", // Adjust column name based on your report
                        ConditionalRules = new List<ConditionalFormattingRuleDto>
                        {
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.TextPattern,
                                Operator = ConditionalFormattingOperator.Contains,
                                TextPattern = "Error",
                                BackgroundColor = "#F8D7DA", // Light red for errors
                                Priority = 1,
                                CaseSensitive = false
                            },
                            new ConditionalFormattingRuleDto
                            {
                                ConditionType = ConditionalFormattingType.TextPattern,
                                Operator = ConditionalFormattingOperator.Contains,
                                TextPattern = "Success",
                                BackgroundColor = "#D4EDDA", // Light green for success
                                Priority = 2,
                                CaseSensitive = false
                            }
                        }
                    }
                }
            };

            var data = await _reportAppService.ExportReportToExcelWithCustomHeaderAsync(input, customHeader);
            var report = await _reportAppService.GetAsync(input.ReportId);

            return File(data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                $"{report.Name}_ConditionalFormatting_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    /// <summary>
    /// Get a paged list of reports with dynamic filtering and sorting
    /// </summary>
    /// <param name="input">Query parameters including filtering and sorting</param>
    /// <returns>Paged list of reports in standard ABP format</returns>
    [HttpPost("list")]
    [Authorize(WismaAppPermissions.PolicyReport.View)]
    [ProducesResponseType(typeof(PagedResultDto<ReportDto>), StatusCodes.Status200OK)]
    public virtual async Task<PagedResultDto<ReportDto>> GetPagedListAsync(QueryParameters input)
    {
        try
        {
            // Get the base query with includes
            var query = await ExecuteDynamicQueryAsync(input);

            // Get total count before paging
            var totalCount = await query.CountAsync();

            var items = await query
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToListAsync();

            // Map to DTOs
            var dtos = ObjectMapper.Map<List<Report>, List<ReportDto>>(items);

            // Return a standard ABP paged result
            return new PagedResultDto<ReportDto>
            {
                TotalCount = totalCount,
                Items = dtos
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting paged list of reports: {Message}", ex.Message);
            throw new UserFriendlyException(
                "Could not retrieve reports list",
                "Error.ReportsList.Failed",
                ex.Message);
        }
    }

    /// <summary>
    /// Execute dynamic query with filtering and sorting
    /// </summary>
    protected virtual async Task<IQueryable<Report>> ExecuteDynamicQueryAsync(QueryParameters parameters)
    {
        // Get base query with includes
        var query = await _repository.GetQueryableAsync();

        // Add includes for related entities if needed
        query = query.AsNoTracking();

        // Check if we need to include deeper relationships based on filter or sort fields
        var fieldsToCheck = new List<string>();

        // Add filter fields
        if (parameters.FilterGroup?.Conditions != null)
        {
            fieldsToCheck.AddRange(parameters.FilterGroup.Conditions
                .Where(c => c.FieldName.Contains('.'))
                .Select(c => c.FieldName));
        }

        // Add sort fields
        if (parameters.Sort?.Count > 0)
        {
            fieldsToCheck.AddRange(parameters.Sort
                .Where(s => s.Field.Contains('.'))
                .Select(s => s.Field));
        }

        // Apply filters
        if (parameters.FilterGroup?.Conditions != null && parameters.FilterGroup.Conditions.Count > 0)
        {
            query = DynamicQueryBuilder<Report>.ApplyFilters(query, parameters.FilterGroup);
        }

        // Apply sorting
        if (parameters.Sort?.Count > 0)
        {
            query = DynamicQueryBuilder<Report>.ApplyMultipleSorting(query, parameters.Sort);
        }
        else if (!string.IsNullOrEmpty(parameters.Sorting))
        {
            // Use the default sorting if provided
            var sortDesc = parameters.Sorting.StartsWith('-');
            var sortField = sortDesc ? parameters.Sorting[1..] : parameters.Sorting;
            query = DynamicQueryBuilder<Report>.ApplySorting(query, sortField, sortDesc);
        }
        else
        {
            // Default sorting by name
            query = query.OrderBy(x => x.Name);
        }

        return query;
    }

    /// <summary>
    /// Test pivot functionality with sample data
    /// </summary>
    /// <param name="reportId">Report ID to test pivot on</param>
    /// <param name="input">Report execution parameters</param>
    /// <returns>Pivot report result</returns>
    [HttpPost("{reportId}/test-pivot")]
    public async Task<IActionResult> TestReportPivotAsync(Guid reportId, ReportExecutionDto input)
    {
        try
        {
            // Create sample pivot configuration for testing
            var pivotConfig = new ReportPivotConfigDto
            {
                PivotColumnField = "Date", // Field to pivot on (creates dynamic columns)
                ValueField = "Amount", // Field to aggregate
                RowGroupFields = new List<string> { "Category", "Type" }, // Fields that remain as rows
                AggregationType = PivotAggregationType.Sum,
                IncludeTotalsRow = true,
                IncludeTotalsColumn = true,
                HeaderFormat = new PivotColumnHeaderFormat
                {
                    DateFormat = "MMM dd", // Format dates as "Jan 01", "Jan 02", etc.
                    Prefix = "",
                    Suffix = ""
                },
                ColumnSorting = new PivotColumnSorting
                {
                    Direction = PivotSortDirection.Ascending,
                    SortType = PivotSortType.Date
                }
            };

            // Validate the pivot configuration first
            var validation = await _reportAppService.ValidateReportPivotConfigAsync(reportId, pivotConfig);
            if (!validation.IsValid)
            {
                return BadRequest(new
                {
                    Message = "Invalid pivot configuration",
                    Errors = validation.ErrorMessages,
                    Warnings = validation.WarningMessages
                });
            }

            // Update the report to use pivot configuration
            await _reportAppService.UpdateReportPivotConfigAsync(reportId, pivotConfig);

            // Execute the pivot report
            var pivotResult = await _reportAppService.PreviewReportAsync(input);

            return Ok(new
            {
                Message = "Pivot report executed successfully",
                PivotConfiguration = pivotConfig,
                ValidationResult = validation,
                ReportData = pivotResult
            });
        }
        catch (Exception ex)
        {
            return BadRequest($"Error testing pivot report: {ex.Message}");
        }
    }
}
